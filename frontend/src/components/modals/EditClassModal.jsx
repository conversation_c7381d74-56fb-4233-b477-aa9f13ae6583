import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { classesAPI, teachersAPI, settingsAPI } from '../../utils/api';
import { X } from 'lucide-react';
import toast from 'react-hot-toast';

export default function EditClassModal({ isOpen, onClose, onSuccess, classData }) {
  const queryClient = useQueryClient();
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue
  } = useForm();

  // Fetch teachers for dropdown
  const { data: teachersData } = useQuery({
    queryKey: ['teachers'],
    queryFn: () => teachersAPI.getAll(),
  });

  // Fetch settings for grades and sections
  const { data: settingsData } = useQuery({
    queryKey: ['settings'],
    queryFn: () => settingsAPI.getSettings(),
  });

  const teachers = teachersData?.data?.data?.teachers || [];
  const grades = settingsData?.data?.grades || ['9', '10', '11', '12'];
  const sections = settingsData?.data?.sections || ['A', 'B', 'C'];

  // Populate form with class data when modal opens
  useEffect(() => {
    if (classData && isOpen) {
      setValue('name', classData.name || '');
      setValue('grade', classData.grade || '');
      setValue('section', classData.section || '');
      setValue('teacher_id', classData.teacher_id || '');
      setValue('subject', classData.subject || '');
      setValue('room', classData.room || '');
      setValue('schedule', classData.schedule || '');
    }
  }, [classData, isOpen, setValue]);

  const updateClassMutation = useMutation({
    mutationFn: (data) => classesAPI.update(classData.id, data),
    onSuccess: () => {
      queryClient.invalidateQueries(['classes']);
      toast.success('Class updated successfully');
      reset();
      onSuccess?.();
    },
    onError: (error) => {
      console.error('Class update error:', error);
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.message ||
                          'Failed to update class';
      toast.error(`Error: ${errorMessage}`);
    },
  });

  const onSubmit = (data) => {
    // Clean up the data before sending
    const cleanData = {
      ...data,
      teacher_id: data.teacher_id ? parseInt(data.teacher_id) : null,
      grade: data.grade, // Keep as string, not integer
      room: data.room || null,
      schedule: data.schedule || null
    };
    console.log('Updating class with data:', cleanData);
    updateClassMutation.mutate(cleanData);
  };

  if (!isOpen || !classData) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
          onClick={onClose}
        />

        {/* Modal */}
        <div className="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              Edit Class
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div>
              <label className="label">Class Name *</label>
              <input
                {...register('name', { required: 'Class name is required' })}
                className="input"
                placeholder="e.g., Mathematics Grade 10-A"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="label">Grade *</label>
                <select
                  {...register('grade', { required: 'Grade is required' })}
                  className="input"
                >
                  <option value="">Select Grade</option>
                  {grades.map(grade => (
                    <option key={grade} value={grade}>{grade}</option>
                  ))}
                </select>
                {errors.grade && (
                  <p className="mt-1 text-sm text-red-600">{errors.grade.message}</p>
                )}
              </div>

              <div>
                <label className="label">Section *</label>
                <select
                  {...register('section', { required: 'Section is required' })}
                  className="input"
                >
                  <option value="">Select Section</option>
                  {sections.map(section => (
                    <option key={section} value={section}>{section}</option>
                  ))}
                </select>
                {errors.section && (
                  <p className="mt-1 text-sm text-red-600">{errors.section.message}</p>
                )}
              </div>
            </div>

            <div>
              <label className="label">Subject *</label>
              <input
                {...register('subject', { required: 'Subject is required' })}
                className="input"
                placeholder="e.g., Mathematics"
              />
              {errors.subject && (
                <p className="mt-1 text-sm text-red-600">{errors.subject.message}</p>
              )}
            </div>

            <div>
              <label className="label">Teacher</label>
              <select
                {...register('teacher_id')}
                className="input"
              >
                <option value="">Select Teacher</option>
                {teachers.map(teacher => (
                  <option key={teacher.id} value={teacher.id}>
                    {teacher.name} - {teacher.subject}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="label">Room</label>
              <input
                {...register('room')}
                className="input"
                placeholder="e.g., Room 101"
              />
            </div>

            <div>
              <label className="label">Schedule</label>
              <input
                {...register('schedule')}
                className="input"
                placeholder="e.g., Mon-Wed-Fri 10:00-11:00"
              />
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="btn-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={updateClassMutation.isPending}
                className="btn-primary"
              >
                {updateClassMutation.isPending ? 'Updating...' : 'Update Class'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
