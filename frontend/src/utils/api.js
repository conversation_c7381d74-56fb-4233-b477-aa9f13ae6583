import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  logout: () => api.post('/auth/logout'),
  getMe: () => api.get('/auth/me'),
  changePassword: (data) => api.post('/auth/change-password', data),
};

// Students API
export const studentsAPI = {
  getAll: (params) => api.get('/students', { params }),
  getById: (id) => api.get(`/students/${id}`),
  create: (data) => api.post('/students', data),
  bulkCreate: (data) => api.post('/students/bulk', data),
  update: (id, data) => api.put(`/students/${id}`, data),
  delete: (id) => api.delete(`/students/${id}`),
  getStats: () => api.get('/students/stats'),
  export: (params) => api.get('/students/export', { params }),
};

// Teachers API
export const teachersAPI = {
  getAll: (params) => {
    const requestParams = { ...params, _t: Date.now() }; // Add timestamp to prevent caching
    return api.get('/teachers', { params: requestParams });
  },
  getById: (id) => api.get(`/teachers/${id}`),
  create: (data) => api.post('/teachers', data),
  bulkCreate: (data) => api.post('/teachers/bulk', data),
  update: (id, data) => api.put(`/teachers/${id}`, data),
  delete: (id) => api.delete(`/teachers/${id}`),
  getStats: () => api.get('/teachers/stats'),
  export: (params) => api.get('/teachers/export', { params }),
};

// Attendance API
export const attendanceAPI = {
  markAttendance: (data) => api.post('/attendance/mark', data),
  getByStudent: (studentId, params) => api.get(`/attendance/student/${studentId}`, { params }),
  getByClass: (classId, params) => api.get(`/attendance/class/${classId}`, { params }),
  getReport: (params) => api.get('/attendance/report', { params }),
  delete: (id) => api.delete(`/attendance/${id}`),
};

// Classes API
export const classesAPI = {
  getAll: (params) => api.get('/classes', { params }),
  getById: (id) => api.get(`/classes/${id}`),
  getByTeacher: (teacherId) => api.get(`/classes/teacher/${teacherId}`),
  getByStudent: (studentId) => api.get(`/classes/student/${studentId}`),
  create: (data) => api.post('/classes', data),
  update: (id, data) => api.put(`/classes/${id}`, data),
  delete: (id) => api.delete(`/classes/${id}`),
};

// Scores API
export const scoresAPI = {
  getByStudent: (studentId, params) => api.get(`/scores/student/${studentId}`, { params }),
  getByTeacher: (teacherId, params) => api.get(`/scores/teacher/${teacherId}`, { params }),
  getAnalytics: (params) => api.get('/scores/analytics', { params }),
  create: (data) => api.post('/scores', data),
  update: (id, data) => api.put(`/scores/${id}`, data),
  delete: (id) => api.delete(`/scores/${id}`),
};

// Upload API
export const uploadAPI = {
  uploadSingle: (file) => {
    const formData = new FormData();
    formData.append('file', file);
    return api.post('/upload/single', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },
  uploadProfile: (file) => {
    const formData = new FormData();
    formData.append('profilePicture', file);
    return api.post('/upload/profile', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },
  uploadDocument: (file) => {
    const formData = new FormData();
    formData.append('document', file);
    return api.post('/upload/document', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },
  uploadReport: (file) => {
    const formData = new FormData();
    formData.append('report', file);
    return api.post('/upload/report', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },
  getFile: (type, filename) => api.get(`/upload/${type}/${filename}`),
  deleteFile: (filename, type) => api.delete(`/upload/${filename}?type=${type}`),
};

// Notifications API
export const notificationsAPI = {
  getAll: (params) => api.get('/notifications', { params }),
  markAsRead: (id) => api.patch(`/notifications/${id}/read`),
  markAllAsRead: () => api.patch('/notifications/read-all'),
  delete: (id) => api.delete(`/notifications/${id}`),
  create: (data) => api.post('/notifications', data),
  broadcast: (data) => api.post('/notifications/broadcast', data),
  cleanupExpired: () => api.delete('/notifications/cleanup/expired'),
};

// Reports API
export const reportsAPI = {
  getAttendance: (params) => api.get('/reports/attendance', { params }),
  getScores: (params) => api.get('/reports/scores', { params }),
  getStudent: (studentId, params) => api.get(`/reports/student/${studentId}`, { params }),
  getOverview: () => api.get('/reports/overview'),
};

export default api;
