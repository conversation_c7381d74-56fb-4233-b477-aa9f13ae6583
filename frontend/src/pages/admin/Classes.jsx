import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { classesAPI } from '../../utils/api';
import { Plus, Search, Edit, Trash2, Eye, BookOpen, Users } from 'lucide-react';
import toast from 'react-hot-toast';
import AddClassModal from '../../components/modals/AddClassModal';
import EditClassModal from '../../components/modals/EditClassModal';

export default function AdminClasses() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedGrade, setSelectedGrade] = useState('');
  const [selectedSection, setSelectedSection] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedClass, setSelectedClass] = useState(null);
  const queryClient = useQueryClient();

  const { data: classesData, isLoading } = useQuery({
    queryKey: ['classes', { grade: selectedGrade, section: selectedSection }],
    queryFn: () => classesAPI.getAll({ grade: selectedGrade, section: selectedSection }),
  });

  const deleteClassMutation = useMutation({
    mutationFn: classesAPI.delete,
    onSuccess: () => {
      queryClient.invalidateQueries(['classes']);
      toast.success('Class deleted successfully');
    },
    onError: (error) => {
      toast.error(error.response?.data?.error || 'Failed to delete class');
    },
  });

  const handleDeleteClass = async (id) => {
    if (window.confirm('Are you sure you want to delete this class?')) {
      deleteClassMutation.mutate(id);
    }
  };

  const handleEditClass = (classItem) => {
    setSelectedClass(classItem);
    setShowEditModal(true);
  };

  const classes = classesData?.data?.classes || [];
  
  const filteredClasses = classes.filter(classItem =>
    classItem.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    classItem.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (classItem.teacher_name && classItem.teacher_name.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Classes</h1>
          <p className="text-gray-600">Manage class schedules and assignments</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="btn-primary"
        >
          <Plus className="h-5 w-5 mr-2" />
          Add Class
        </button>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search classes..."
              className="input pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <select
            className="input"
            value={selectedGrade}
            onChange={(e) => setSelectedGrade(e.target.value)}
          >
            <option value="">All Grades</option>
            <option value="9">Grade 9</option>
            <option value="10">Grade 10</option>
            <option value="11">Grade 11</option>
            <option value="12">Grade 12</option>
          </select>
          <select
            className="input"
            value={selectedSection}
            onChange={(e) => setSelectedSection(e.target.value)}
          >
            <option value="">All Sections</option>
            <option value="A">Section A</option>
            <option value="B">Section B</option>
            <option value="C">Section C</option>
          </select>
          <div className="text-sm text-gray-500 flex items-center">
            Total: {filteredClasses.length} classes
          </div>
        </div>
      </div>

      {/* Classes Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredClasses.map((classItem) => (
          <div key={classItem.id} className="card hover:shadow-md transition-shadow">
            <div className="flex items-start justify-between">
              <div className="flex items-center">
                <div className="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center">
                  <BookOpen className="h-6 w-6 text-primary-600" />
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-medium text-gray-900">{classItem.name}</h3>
                  <p className="text-sm text-gray-500">{classItem.subject}</p>
                </div>
              </div>
              <div className="flex space-x-1">
                <button className="text-primary-600 hover:text-primary-900">
                  <Eye className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleEditClass(classItem)}
                  className="text-gray-600 hover:text-gray-900"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDeleteClass(classItem.id)}
                  className="text-red-600 hover:text-red-900"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>

            <div className="mt-4 space-y-2">
              <div className="flex items-center text-sm text-gray-600">
                <Users className="h-4 w-4 mr-2" />
                Grade {classItem.grade} - Section {classItem.section}
              </div>
              
              {classItem.teacher_name && (
                <div className="text-sm text-gray-600">
                  <span className="font-medium">Teacher:</span> {classItem.teacher_name}
                </div>
              )}
              
              {classItem.schedule && (
                <div className="text-sm text-gray-600">
                  <span className="font-medium">Schedule:</span> {classItem.schedule}
                </div>
              )}

              {classItem.virtual_link && (
                <div className="mt-3">
                  <a
                    href={classItem.virtual_link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full hover:bg-blue-200"
                  >
                    Join Virtual Class
                  </a>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {filteredClasses.length === 0 && (
        <div className="text-center py-12">
          <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <div className="text-gray-500">No classes found</div>
          <button
            onClick={() => setShowAddModal(true)}
            className="btn-primary mt-4"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add First Class
          </button>
        </div>
      )}

      {/* Add Class Modal */}
      {showAddModal && (
        <AddClassModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          onSuccess={() => {
            setShowAddModal(false);
            queryClient.invalidateQueries(['classes']);
          }}
        />
      )}

      {/* Edit Class Modal */}
      {showEditModal && (
        <EditClassModal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setSelectedClass(null);
          }}
          onSuccess={() => {
            setShowEditModal(false);
            setSelectedClass(null);
            queryClient.invalidateQueries(['classes']);
          }}
          classData={selectedClass}
        />
      )}
    </div>
  );
}
