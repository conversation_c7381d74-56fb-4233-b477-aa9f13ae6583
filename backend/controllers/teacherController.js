const User = require('../models/User');
const Teacher = require('../models/Teacher');
const bcrypt = require('bcryptjs');
const { bulkCreateTeachers } = require('../utils/userUtils');

const teacherController = {
  // Get all teachers
  getAllTeachers: (req, res) => {
    try {
      const teachers = Teacher.getAll();
      res.json({
        success: true,
        data: { teachers }
      });
    } catch (error) {
      console.error('Error fetching teachers:', error);
      res.status(500).json({ error: 'Failed to fetch teachers' });
    }
  },

  // Get teacher stats
  getTeacherStats: (req, res) => {
    try {
      const teachers = Teacher.getAll();
      const stats = {
        total: teachers.length,
        bySubject: teachers.reduce((acc, teacher) => {
          acc[teacher.subject] = (acc[teacher.subject] || 0) + 1;
          return acc;
        }, {}),
        avgExperience: teachers.length > 0 ? 
          teachers.reduce((sum, t) => sum + (t.experience || 0), 0) / teachers.length : 0
      };
      
      res.json({
        success: true,
        data: { stats }
      });
    } catch (error) {
      console.error('Error fetching teacher stats:', error);
      res.status(500).json({ error: 'Failed to fetch teacher stats' });
    }
  },

  // Get teacher by ID
  getTeacherById: (req, res) => {
    try {
      const teacher = Teacher.findById(req.params.id);
      if (!teacher) {
        return res.status(404).json({ error: 'Teacher not found' });
      }
      
      res.json({
        success: true,
        data: { teacher }
      });
    } catch (error) {
      console.error('Error fetching teacher:', error);
      res.status(500).json({ error: 'Failed to fetch teacher' });
    }
  },

  // Create new teacher
  createTeacher: async (req, res) => {
    try {
      const { email, password, name, phone, teacher_id, subject, qualification, experience } = req.body;

      // Check if email already exists
      const existingUser = User.findByEmail(email);
      if (existingUser) {
        return res.status(400).json({ error: 'Email already exists' });
      }

      // Check if teacher_id already exists
      const existingTeacher = Teacher.findByTeacherId(teacher_id);
      if (existingTeacher) {
        return res.status(400).json({ error: 'Teacher ID already exists' });
      }

      // Create user first (User.create handles password hashing internally)
      const user = User.create({
        email,
        password, // Don't hash here - User.create() handles it
        name,
        phone,
        role: 'teacher'
      });

      // Create teacher profile
      const teacher = Teacher.create({
        user_id: user.id,
        teacher_id,
        subject,
        qualification,
        experience: experience || 0
      });

      // Get complete teacher data
      const completeTeacher = Teacher.findById(teacher.id);

      console.log(`✅ Teacher created: ${email} (ID: ${teacher_id})`);

      res.status(201).json({
        message: 'Teacher created successfully',
        teacher: completeTeacher
      });
    } catch (error) {
      console.error('Error creating teacher:', error);
      res.status(500).json({ error: 'Failed to create teacher' });
    }
  },

  // Update teacher
  updateTeacher: (req, res) => {
    try {
      const teacher = Teacher.findById(req.params.id);
      if (!teacher) {
        return res.status(404).json({ error: 'Teacher not found' });
      }

      const { name, email, teacher_id, subject, qualification, experience, phone } = req.body;

      // Check if email is being changed and if it already exists
      if (email && email !== teacher.email) {
        const existingUser = User.findByEmail(email);
        if (existingUser && existingUser.id !== teacher.user_id) {
          return res.status(400).json({ error: 'Email already exists' });
        }
      }

      // Check if teacher_id is being changed and if it already exists
      if (teacher_id && teacher_id !== teacher.teacher_id) {
        const existingTeacher = Teacher.findByTeacherId(teacher_id);
        if (existingTeacher && existingTeacher.id !== teacher.id) {
          return res.status(400).json({ error: 'Teacher ID already exists' });
        }
      }

      // Update user fields if provided
      const userUpdateData = {};
      if (name !== undefined) userUpdateData.name = name;
      if (email !== undefined) userUpdateData.email = email;
      if (phone !== undefined) userUpdateData.phone = phone;

      if (Object.keys(userUpdateData).length > 0) {
        User.update(teacher.user_id, userUpdateData);
      }

      // Update teacher fields if provided
      const teacherUpdateData = {};
      if (teacher_id !== undefined) teacherUpdateData.teacher_id = teacher_id;
      if (subject !== undefined) teacherUpdateData.subject = subject;
      if (qualification !== undefined) teacherUpdateData.qualification = qualification;
      if (experience !== undefined) teacherUpdateData.experience = experience;

      if (Object.keys(teacherUpdateData).length > 0) {
        Teacher.update(req.params.id, teacherUpdateData);
      }

      // Get the updated teacher data
      const updatedTeacher = Teacher.findById(req.params.id);

      res.json({
        message: 'Teacher updated successfully',
        teacher: updatedTeacher
      });
    } catch (error) {
      console.error('Error updating teacher:', error);
      res.status(500).json({ error: 'Failed to update teacher' });
    }
  },

  // Delete teacher
  deleteTeacher: (req, res) => {
    try {
      const teacher = Teacher.findById(req.params.id);
      if (!teacher) {
        return res.status(404).json({ error: 'Teacher not found' });
      }

      // Delete teacher profile
      Teacher.delete(req.params.id);
      
      // Delete user account
      User.delete(teacher.user_id);

      res.json({ message: 'Teacher deleted successfully' });
    } catch (error) {
      console.error('Error deleting teacher:', error);
      res.status(500).json({ error: 'Failed to delete teacher' });
    }
  },

  // Bulk create teachers
  bulkCreateTeachers: async (req, res) => {
    try {
      const { teachers } = req.body;

      if (!Array.isArray(teachers) || teachers.length === 0) {
        return res.status(400).json({ error: 'Teachers array is required and cannot be empty' });
      }

      if (teachers.length > 50) {
        return res.status(400).json({ error: 'Cannot create more than 50 teachers at once' });
      }

      const results = await bulkCreateTeachers(teachers);

      res.status(201).json({
        message: `Bulk teacher creation completed. ${results.success.length} successful, ${results.failed.length} failed.`,
        results: results
      });

    } catch (error) {
      console.error('Bulk create teachers error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
};

module.exports = teacherController;
