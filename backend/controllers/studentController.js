const User = require('../models/User');
const Student = require('../models/Student');
const { bulkCreateStudents } = require('../utils/userUtils');

const createStudent = async (req, res) => {
  try {
    const { email, password, name, phone, student_id, grade, section, parent_name, parent_phone, address } = req.body;

    // Check if email already exists
    const existingUser = User.findByEmail(email);
    if (existingUser) {
      return res.status(400).json({ error: 'Email already exists' });
    }

    // Check if student_id already exists
    const existingStudent = Student.findByStudentId(student_id);
    if (existingStudent) {
      return res.status(400).json({ error: 'Student ID already exists' });
    }

    // Create user account
    const user = User.create({
      email,
      password,
      role: 'student',
      name,
      phone
    });

    // Create student profile
    const student = Student.create({
      user_id: user.id,
      student_id,
      grade,
      section,
      parent_name,
      parent_phone,
      address
    });

    console.log(`✅ Student created: ${email} (ID: ${student_id})`);

    res.status(201).json({
      message: 'Student created successfully',
      student
    });

  } catch (error) {
    console.error('Create student error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const getAllStudents = async (req, res) => {
  try {
    const { grade, section } = req.query;
    const filters = {};
    
    if (grade) filters.grade = grade;
    if (section) filters.section = section;

    const students = Student.findAll(filters);
    res.json({ students });

  } catch (error) {
    console.error('Get students error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const getStudentById = async (req, res) => {
  try {
    const { id } = req.params;
    const student = Student.findById(id);

    if (!student) {
      return res.status(404).json({ error: 'Student not found' });
    }

    // Get additional stats
    const attendanceStats = Student.getAttendanceStats(id);
    const scoreStats = Student.getScoreStats(id);

    res.json({
      student,
      stats: {
        attendance: attendanceStats,
        scores: scoreStats
      }
    });

  } catch (error) {
    console.error('Get student error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const updateStudent = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, email, student_id, grade, section, phone, parent_name, parent_phone, address } = req.body;

    const student = Student.findById(id);
    if (!student) {
      return res.status(404).json({ error: 'Student not found' });
    }

    // Check if email is being changed and if it already exists
    if (email && email !== student.email) {
      const existingUser = User.findByEmail(email);
      if (existingUser && existingUser.id !== student.user_id) {
        return res.status(400).json({ error: 'Email already exists' });
      }
    }

    // Check if student_id is being changed and if it already exists
    if (student_id && student_id !== student.student_id) {
      const existingStudent = Student.findByStudentId(student_id);
      if (existingStudent && existingStudent.id !== student.id) {
        return res.status(400).json({ error: 'Student ID already exists' });
      }
    }

    // Update user fields if provided
    const userUpdateData = {};
    if (name !== undefined) userUpdateData.name = name;
    if (email !== undefined) userUpdateData.email = email;
    if (phone !== undefined) userUpdateData.phone = phone;

    if (Object.keys(userUpdateData).length > 0) {
      User.update(student.user_id, userUpdateData);
    }

    // Update student fields if provided
    const studentUpdateData = {};
    if (student_id !== undefined) studentUpdateData.student_id = student_id;
    if (grade !== undefined) studentUpdateData.grade = grade;
    if (section !== undefined) studentUpdateData.section = section;
    if (parent_name !== undefined) studentUpdateData.parent_name = parent_name;
    if (parent_phone !== undefined) studentUpdateData.parent_phone = parent_phone;
    if (address !== undefined) studentUpdateData.address = address;

    if (Object.keys(studentUpdateData).length > 0) {
      Student.update(id, studentUpdateData);
    }

    // Get the updated student data
    const updatedStudent = Student.findById(id);

    res.json({
      message: 'Student updated successfully',
      student: updatedStudent
    });

  } catch (error) {
    console.error('Update student error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const deleteStudent = async (req, res) => {
  try {
    const { id } = req.params;

    const student = Student.findById(id);
    if (!student) {
      return res.status(404).json({ error: 'Student not found' });
    }

    // Delete student (this will also delete the user due to CASCADE)
    const deleted = Student.delete(id);
    if (deleted) {
      // Also delete the user account
      User.delete(student.user_id);
    }

    res.json({ message: 'Student deleted successfully' });

  } catch (error) {
    console.error('Delete student error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const getStudentStats = async (req, res) => {
  try {
    const gradeStats = Student.getGradeStats();
    const userStats = User.getStats();

    res.json({
      gradeStats,
      userStats
    });

  } catch (error) {
    console.error('Get student stats error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const bulkCreateStudentsController = async (req, res) => {
  try {
    const { students } = req.body;

    if (!Array.isArray(students) || students.length === 0) {
      return res.status(400).json({ error: 'Students array is required and cannot be empty' });
    }

    if (students.length > 100) {
      return res.status(400).json({ error: 'Cannot create more than 100 students at once' });
    }

    const results = await bulkCreateStudents(students);

    res.status(201).json({
      message: `Bulk student creation completed. ${results.success.length} successful, ${results.failed.length} failed.`,
      results: results
    });

  } catch (error) {
    console.error('Bulk create students error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  createStudent,
  getAllStudents,
  getStudentById,
  updateStudent,
  deleteStudent,
  getStudentStats,
  bulkCreateStudents: bulkCreateStudentsController
};
